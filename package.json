{"name": "@playcanvas/splat-transform", "version": "0.8.1", "author": "PlayCanvas<<EMAIL>>", "homepage": "https://playcanvas.com", "description": "CLI tool for 3D Gaussian splat format conversion and transformation", "keywords": ["3d-gaussian-splatting", "cli", "gaussian-splatting", "playcanvas", "supersplat", "typescript"], "license": "MIT", "bin": {"splat-transform": "bin/cli.mjs"}, "bugs": {"url": "https://github.com/playcanvas/splat-transform/issues"}, "repository": {"type": "git", "url": "git+https://github.com/playcanvas/splat-transform.git"}, "files": ["dist/", "lib/"], "devDependencies": {"@playcanvas/eslint-config": "^2.1.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^24.3.1", "@typescript-eslint/eslint-plugin": "^8.43.0", "@typescript-eslint/parser": "^8.43.0", "eslint": "^9.35.0", "eslint-import-resolver-typescript": "^4.4.4", "playcanvas": "^2.11.2", "publint": "^0.3.12", "rollup": "^4.50.1", "tslib": "^2.8.1", "typescript": "^5.9.2"}, "dependencies": {"webgpu": "^0.3.0"}, "scripts": {"build": "rollup -c", "lint": "eslint src rollup.config.mjs eslint.config.mjs", "lint:fix": "eslint src rollup.config.mjs eslint.config.mjs --fix", "publint": "publint", "watch": "rollup -c -w"}, "engines": {"node": ">=18.0.0"}}